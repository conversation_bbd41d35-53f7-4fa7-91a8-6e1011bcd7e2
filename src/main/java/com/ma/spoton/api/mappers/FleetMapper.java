package com.ma.spoton.api.mappers;

import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import com.ma.spoton.api.constants.BusinessConstants;
import com.ma.spoton.api.dtos.FleetCsvExportDto;
import com.ma.spoton.api.dtos.FleetDto;
import com.ma.spoton.api.dtos.FleetExportDto;
import com.ma.spoton.api.entities.Fleet;
import com.ma.spoton.api.requests.FleetRequest;
import com.ma.spoton.api.utils.DateTimeUtils;
import com.ma.spoton.api.entities.Client;

@Mapper(componentModel = "spring",
    imports = {Objects.class, DateTimeUtils.class, BusinessConstants.class},
    uses = {SpotMapper.class, ClientMapper.class})
public interface FleetMapper {

  Fleet mapToEntity(FleetRequest fleetRequest);

  Fleet updateEntity(FleetRequest fleetRequest, @MappingTarget Fleet fleet);

  @Mapping(target = "fleetId", source = "fleet.uuid")
  @Mapping(target = "clients", source = "fleet.clients")
  @Mapping(target = "audit",
      expression = "java( new AuditMapperImpl(new UserMapperImpl()).mapToDto(fleet, timeZone) )")
//  @Mapping(target = "carrier", source = "fleet.carriers")
  FleetDto mapToDto(Fleet fleet, String timeZone);

  @Mapping(target = "fleetId", source = "fleet.uuid")
  @Mapping(target = "spot", ignore = true)
  @Mapping(target = "audit", ignore = true)
  @Mapping(target = "clients", ignore = true)
//  @Mapping(target = "carrier", source = "fleet.carriers")
  FleetDto mapToDto(Fleet fleet);

//  @Mapping(target = "carrier", source = "fleet.carriers.carrier")
  @Mapping(target = "createdDate",
      expression = "java( Objects.nonNull(fleet.getCreatedDate()) ? DateTimeUtils.convertZonedDateTimeToString(fleet.getCreatedDate(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
  @Mapping(target = "createdBy", expression = "java(fleet.getCreatedBy().getFirstName() + \" \" + fleet.getCreatedBy().getLastName())")
  @Mapping(target = "lastModifiedDate", 
		  expression = "java( Objects.nonNull(fleet.getLastModifiedDate()) ? DateTimeUtils.convertZonedDateTimeToString(fleet.getLastModifiedDate(), BusinessConstants.DISPLAY_DATE_TIME_FORMAT, timeZone) : \"\" )")
  FleetExportDto mapToExportDto(Fleet fleet, String timeZone);

  FleetCsvExportDto mapToFleetCsvExportDto(FleetExportDto fleetExportDto);
}
